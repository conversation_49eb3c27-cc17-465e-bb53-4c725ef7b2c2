import { DOMUtils } from '../utils/dom.js';
import { Validator, VALIDATION_RULES } from '../utils/validation.js';
import authService from '../services/authService.js';

export class AuthForm {
  constructor(containerId, onAuthSuccess) {
    this.container = DOMUtils.getElementById(containerId);
    this.currentForm = 'login'; // 'login', 'register'
    this.onAuthSuccess = onAuthSuccess;
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
  }

  render() {
    const content = this.renderAuthForm();
    DOMUtils.setContent(this.container, content);
  }

  // Render combined auth form (login + register)
  renderAuthForm() {
    return `
      <div class="min-h-screen flex auth-container">
        <!-- Left Side - Branding (70% width) -->
        <div class="bg-gradient-to-br from-slate-900 via-slate-800 to-gray-900 relative overflow-hidden auth-left-panel" style="flex: 0 0 65%;">
          <!-- Modern Grid Pattern -->
          <div class="absolute inset-0 opacity-[0.03]" style="background-image:
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 40px 40px;"></div>

          <!-- Floating geometric shapes -->
          <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full opacity-10 blur-3xl animate-pulse"></div>
          <div class="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500 rounded-full opacity-10 blur-2xl animate-pulse" style="animation-delay: 2s;"></div>

          <div class="flex flex-col justify-center h-full p-12 relative z-10">
            <div class="text-white text-center relative z-10 fade-in-up">
              <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 backdrop-blur-sm shadow-2xl hover:scale-105 transition-transform duration-300">
                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
              </div>
              <h1 class="text-5xl font-bold mb-4 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">Peta Talenta</h1>
              <p class="text-slate-300 text-xl mb-12 font-light">Platform Pemetaan dan Analisis Talenta Berbasis AI</p>
            </div>

            <div class="grid grid-cols-3 gap-8 max-w-4xl mx-auto mt-16">
              <div class="text-center text-white feature-card" style="animation-delay: 0.2s;">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm border border-white/10 hover:scale-110 transition-all duration-300">
                  <svg class="w-7 h-7 text-blue-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 class="font-semibold mb-3 text-lg">Asesmen Cerdas</h3>
                <p class="text-slate-400 text-sm leading-relaxed">Algoritma AI canggih untuk evaluasi talenta yang akurat</p>
              </div>

              <div class="text-center text-white feature-card" style="animation-delay: 0.4s;">
                <div class="w-14 h-14 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm border border-white/10 hover:scale-110 transition-all duration-300">
                  <svg class="w-7 h-7 text-purple-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
                <h3 class="font-semibold mb-3 text-lg">Wawasan Bertenaga AI</h3>
                <p class="text-slate-400 text-sm leading-relaxed">Rekomendasi berbasis data untuk keputusan yang lebih baik</p>
              </div>

              <div class="text-center text-white feature-card" style="animation-delay: 0.6s;">
                <div class="w-14 h-14 bg-gradient-to-br from-green-500/20 to-blue-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm border border-white/10 hover:scale-110 transition-all duration-300">
                  <svg class="w-7 h-7 text-green-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 class="font-semibold mb-3 text-lg">Analitik Real-time</h3>
                <p class="text-slate-400 text-sm leading-relaxed">Pantau kemajuan dan kinerja secara real-time</p>
              </div>
            </div>

            <div class="text-center mt-16 fade-in-up" style="animation-delay: 0.8s;">
              <p class="text-slate-400 text-base italic">"Temukan potensi terbaik Anda dengan Peta Talenta"</p>
            </div>
          </div>
        </div>

        <!-- Right Side - Auth Form (30% width) -->
        <div class="bg-gray-50 flex items-center justify-center p-8 auth-right-panel" style="flex: 0 0 35%;">
          <div class="w-full max-w-md">
            <div class="text-center mb-10 fade-in-up">
              <div class="flex mb-10 bg-white rounded-2xl p-1.5 shadow-lg border border-gray-200/50">
                <div class="flex-1 py-3 px-6 text-center rounded-xl transition-all duration-300 cursor-pointer ${this.currentForm === 'login' ? 'bg-gradient-to-r from-slate-800 to-slate-700 text-white font-semibold shadow-lg transform scale-105' : 'text-slate-600 hover:text-slate-800 hover:bg-gray-50'}" id="loginTab">Masuk</div>
                <div class="flex-1 py-3 px-6 text-center rounded-xl transition-all duration-300 cursor-pointer ${this.currentForm === 'register' ? 'bg-gradient-to-r from-slate-800 to-slate-700 text-white font-semibold shadow-lg transform scale-105' : 'text-slate-600 hover:text-slate-800 hover:bg-gray-50'}" id="registerTab">Daftar</div>
              </div>
              <h2 class="text-3xl font-bold text-slate-800 mb-3 tracking-tight">${this.currentForm === 'login' ? 'Selamat Datang Kembali' : 'Buat Akun Anda'}</h2>
              <p class="text-slate-500 text-lg">${this.currentForm === 'login' ? 'Masuk ke akun Anda untuk melanjutkan' : 'Bergabunglah dengan Peta Talenta hari ini'}</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="space-y-8 form-container ${this.currentForm === 'login' ? '' : 'hidden'}">
              <div class="form-group">
                <label for="loginEmail" class="block text-sm font-semibold text-slate-700 mb-3">Alamat Email</label>
                <input type="email" id="loginEmail" name="email" class="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent bg-white placeholder-slate-400 text-slate-700 transition-all duration-300 hover:border-slate-300" placeholder="Masukkan email Anda" required>
                <div id="loginEmailError" class="text-red-500 text-sm mt-2 hidden font-medium"></div>
              </div>

              <div class="form-group">
                <label for="loginPassword" class="block text-sm font-semibold text-slate-700 mb-3">Kata Sandi</label>
                <input type="password" id="loginPassword" name="password" class="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent bg-white placeholder-slate-400 text-slate-700 transition-all duration-300 hover:border-slate-300" placeholder="Masukkan kata sandi Anda" required>
                <div id="loginPasswordError" class="text-red-500 text-sm mt-2 hidden font-medium"></div>
              </div>

              <div id="loginError" class="text-red-500 text-sm mt-2 hidden font-medium bg-red-50 p-3 rounded-lg border border-red-200"></div>

              <button type="submit" id="loginBtn" class="w-full bg-gradient-to-r from-slate-800 to-slate-700 hover:from-slate-700 hover:to-slate-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                Masuk
              </button>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="space-y-6 form-container ${this.currentForm === 'register' ? '' : 'hidden'}">
              <div class="form-group">
                <label for="registerEmail" class="block text-sm font-semibold text-slate-700 mb-3">Alamat Email</label>
                <input type="email" id="registerEmail" name="email" class="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent bg-white placeholder-slate-400 text-slate-700 transition-all duration-300 hover:border-slate-300" placeholder="Masukkan email Anda" required>
                <div id="registerEmailError" class="text-red-500 text-sm mt-2 hidden font-medium"></div>
              </div>

              <div class="form-group">
                <label for="registerPassword" class="block text-sm font-semibold text-slate-700 mb-3">Kata Sandi</label>
                <input type="password" id="registerPassword" name="password" class="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent bg-white placeholder-slate-400 text-slate-700 transition-all duration-300 hover:border-slate-300" placeholder="Masukkan kata sandi Anda" required>
                <div id="registerPasswordError" class="text-red-500 text-sm mt-2 hidden font-medium"></div>
              </div>

              <div class="form-group">
                <label for="confirmPassword" class="block text-sm font-semibold text-slate-700 mb-3">Konfirmasi Kata Sandi</label>
                <input type="password" id="confirmPassword" name="confirmPassword" class="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent bg-white placeholder-slate-400 text-slate-700 transition-all duration-300 hover:border-slate-300" placeholder="Konfirmasi kata sandi Anda" required>
                <div id="confirmPasswordError" class="text-red-500 text-sm mt-2 hidden font-medium"></div>
              </div>

              <div class="flex items-start space-x-3 mb-6">
                <input type="checkbox" id="agreeTerms" name="agreeTerms" class="w-5 h-5 text-slate-600 border-2 border-gray-300 rounded-lg focus:ring-slate-500 mt-0.5" required>
                <label for="agreeTerms" class="text-sm text-slate-600 leading-relaxed">
                  Saya setuju dengan <a href="#" class="text-slate-800 hover:text-slate-600 underline font-medium">Syarat dan Ketentuan</a> serta <a href="#" class="text-slate-800 hover:text-slate-600 underline font-medium">Kebijakan Privasi</a>
                </label>
              </div>

              <div id="registerError" class="text-red-500 text-sm mt-2 hidden font-medium bg-red-50 p-3 rounded-lg border border-red-200"></div>

              <button type="submit" id="registerBtn" class="w-full bg-gradient-to-r from-slate-800 to-slate-700 hover:from-slate-700 hover:to-slate-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                </svg>
                Buat Akun
              </button>
            </form>

            <div class="mt-8 text-center fade-in-up" style="animation-delay: 0.4s;">
              <p class="text-sm text-slate-400">© 2024 Peta Talenta. Semua hak dilindungi.</p>
            </div>
          </div>
        </div>
      </div>
    `;
  }



  attachEventListeners() {
    // Remove existing listeners
    this.container.removeEventListener('click', this.handleClick);
    this.container.removeEventListener('submit', this.handleSubmit);
    
    // Add new listeners
    this.container.addEventListener('click', this.handleClick.bind(this));
    this.container.addEventListener('submit', this.handleSubmit.bind(this));
  }

  handleClick(event) {
    const { target } = event;

    if (target.id === 'showRegisterBtn' || target.id === 'registerTab') {
      this.switchToTab('register');
    } else if (target.id === 'showLoginBtn' || target.id === 'loginTab') {
      this.switchToTab('login');
    }
  }

  // Switch between login and register tabs without full re-render
  switchToTab(formType) {
    this.currentForm = formType;

    // Update tab active states
    const loginTab = DOMUtils.getElementById('loginTab');
    const registerTab = DOMUtils.getElementById('registerTab');
    const loginForm = DOMUtils.getElementById('loginForm');
    const registerForm = DOMUtils.getElementById('registerForm');
    const formTitle = document.querySelector('h2');
    const formSubtitle = document.querySelector('p.text-gray-600');

    if (loginTab && registerTab && loginForm && registerForm && formTitle && formSubtitle) {
      // Update tab states
      if (formType === 'login') {
        // Update tab classes
        loginTab.className = 'flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer bg-white text-blue-600 font-medium shadow-sm';
        registerTab.className = 'flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer text-gray-600 hover:text-gray-800';

        loginForm.classList.remove('hidden');
        registerForm.classList.add('hidden');
        formTitle.textContent = 'Selamat Datang Kembali';
        formSubtitle.textContent = 'Masuk ke akun Anda untuk melanjutkan';
      } else {
        // Update tab classes
        loginTab.className = 'flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer text-gray-600 hover:text-gray-800';
        registerTab.className = 'flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer bg-white text-blue-600 font-medium shadow-sm';

        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');
        formTitle.textContent = 'Buat Akun Anda';
        formSubtitle.textContent = 'Bergabunglah dengan Peta Talenta hari ini';
      }

      // Clear any error messages
      this.clearAllErrors();
    }
  }

  // Clear all error and success messages
  clearAllErrors() {
    const errorElements = this.container.querySelectorAll('.error-message, .success-message');
    errorElements.forEach(el => {
      el.classList.add('hidden');
      el.textContent = '';
    });
  }

  async handleSubmit(event) {
    event.preventDefault();
    const form = event.target;

    if (form.id === 'loginForm') {
      await this.handleLogin(form);
    } else if (form.id === 'registerForm') {
      await this.handleRegister(form);
    }
  }

  async handleLogin(form) {
    const formData = DOMUtils.getFormData(form);
    const submitBtn = DOMUtils.getElementById('loginBtn');
    const errorEl = DOMUtils.getElementById('loginError');

    // Clear previous messages
    DOMUtils.hideError(errorEl);

    // Validate form
    const validation = Validator.validateForm(formData, {
      email: VALIDATION_RULES.email,
      password: VALIDATION_RULES.password
    });

    if (!validation.isValid) {
      this.showFieldErrors('login', validation.errors);
      return;
    }

    try {
      DOMUtils.setLoading(submitBtn, true);

      const result = await authService.login(formData.email, formData.password);

      if (result.success) {
        // Langsung redirect tanpa menampilkan pesan sukses
        if (this.onAuthSuccess) {
          this.onAuthSuccess();
        }
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }

  async handleRegister(form) {
    const formData = DOMUtils.getFormData(form);
    const submitBtn = DOMUtils.getElementById('registerBtn');
    const errorEl = DOMUtils.getElementById('registerError');

    // Clear previous messages
    DOMUtils.hideError(errorEl);

    // Check if passwords match
    if (formData.password !== formData.confirmPassword) {
      const confirmPasswordError = DOMUtils.getElementById('confirmPasswordError');
      DOMUtils.showError(confirmPasswordError, 'Kata sandi tidak cocok');
      return;
    }

    // Check terms agreement
    const agreeTermsCheckbox = DOMUtils.getElementById('agreeTerms');
    if (!agreeTermsCheckbox || !agreeTermsCheckbox.checked) {
      DOMUtils.showError(errorEl, 'Anda harus menyetujui Syarat dan Ketentuan');
      return;
    }

    // Validate form
    const validation = Validator.validateForm(formData, {
      email: VALIDATION_RULES.email,
      password: VALIDATION_RULES.password
    });

    if (!validation.isValid) {
      this.showFieldErrors('register', validation.errors);
      return;
    }

    try {
      DOMUtils.setLoading(submitBtn, true);

      const result = await authService.register(formData.email, formData.password);

      if (result.success) {
        // Langsung redirect tanpa menampilkan pesan sukses
        if (this.onAuthSuccess) {
          this.onAuthSuccess();
        }
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }





  showFieldErrors(formType, errors) {
    Object.entries(errors).forEach(([field, message]) => {
      const errorEl = DOMUtils.getElementById(`${formType}${field.charAt(0).toUpperCase() + field.slice(1)}Error`) ||
                     DOMUtils.getElementById(`${field}Error`);
      if (errorEl) {
        DOMUtils.showError(errorEl, message);
      }
    });
  }

  // Public method to switch forms
  switchToForm(formType) {
    this.currentForm = formType;
    this.render();
    this.attachEventListeners();
  }

  // Public method to check auth state and show appropriate form
  checkAuthState() {
    if (authService.isAuthenticated()) {
      // If authenticated, trigger callback to show profile page
      if (this.onAuthSuccess) {
        this.onAuthSuccess();
      }
    } else {
      this.currentForm = 'login';
      this.render();
      this.attachEventListeners();
    }
  }
}
