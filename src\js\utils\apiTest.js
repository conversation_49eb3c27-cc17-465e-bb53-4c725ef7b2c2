// API Test utility untuk testing koneksi
import { API_CONFIG } from '../config/api.js';
import { DEBUG_CONFIG, debugLog } from '../config/debug.js';

export class APITest {
  static async testConnection() {
    try {
      // Silent test - only test basic connectivity
      const healthResponse = await fetch(`${API_CONFIG.BASE_URL}`, {
        method: 'GET',
        headers: API_CONFIG.HEADERS
      });

      // If we can reach the server, test an API endpoint
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: API_CONFIG.HEADERS,
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpassword123'
        })
      });

      // 401 is expected for invalid credentials - this means API is working
      // 404 means endpoint not found - API issue
      // Network errors will be caught below
      return response.status !== 404;
    } catch (error) {
      // Only log actual network/connection errors
      return false;
    }
  }
  
  static async testRegister(email = '<EMAIL>', password = 'testpass123') {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.AUTH.REGISTER}`, {
        method: 'POST',
        headers: API_CONFIG.HEADERS,
        body: JSON.stringify({ email, password })
      });
      
      const data = await response.json();
      console.log('Register Test Response:', data);
      
      return { success: response.ok, data, status: response.status };
    } catch (error) {
      console.error('Register Test Error:', error);
      return { success: false, error: error.message };
    }
  }
}

// Auto-test connection when in development (controlled by debug config)
if (DEBUG_CONFIG.IS_DEV && DEBUG_CONFIG.ENABLE_AUTO_API_TEST) {
  // Silent API test - only log if there are actual connection issues
  APITest.testConnection().then(isWorking => {
    if (!isWorking) {
      console.warn('⚠️ API connection issue detected. Check your internet connection or API server status.');
    } else {
      debugLog('api', '✅ API connection verified');
    }
  }).catch(error => {
    console.warn('⚠️ API connection test failed:', error.message);
  });
}
