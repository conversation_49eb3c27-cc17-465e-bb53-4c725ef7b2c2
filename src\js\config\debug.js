// Debug configuration
export const DEBUG_CONFIG = {
  // Enable/disable debug logging
  ENABLE_API_LOGGING: false,
  
  // Enable/disable auto API testing
  ENABLE_AUTO_API_TEST: false,
  
  // Enable/disable validation logging
  ENABLE_VALIDATION_LOGGING: false,
  
  // Check if we're in development mode
  IS_DEV: import.meta.env.DEV
};

// Helper function to log only when debugging is enabled
export const debugLog = (category, ...args) => {
  if (!DEBUG_CONFIG.IS_DEV) return;
  
  switch (category) {
    case 'api':
      if (DEBUG_CONFIG.ENABLE_API_LOGGING) {
        console.log('[API]', ...args);
      }
      break;
    case 'validation':
      if (DEBUG_CONFIG.ENABLE_VALIDATION_LOGGING) {
        console.log('[VALIDATION]', ...args);
      }
      break;
    default:
      console.log('[DEBUG]', ...args);
  }
};
