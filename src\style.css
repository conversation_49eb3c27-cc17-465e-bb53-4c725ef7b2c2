@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500;
  }

  .form-group {
    @apply mb-6;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .error-message {
    @apply text-red-600 text-sm mt-1;
  }

  .success-message {
    @apply text-green-600 text-sm mt-1;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .loading {
    @apply opacity-50 pointer-events-none;
  }

  /* Modern Auth Page Animations */
  .auth-container {
    animation: fadeIn 0.8s ease-out;
  }

  .auth-left-panel {
    animation: slideInLeft 1s ease-out;
  }

  .auth-right-panel {
    animation: slideInRight 1s ease-out;
  }

  .fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
  }

  .feature-card {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .form-container {
    animation: fadeInUp 1.2s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
  }

  /* Keyframe Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Input Focus Effects */
  input:focus {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Button Hover Effects */
  button:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Loading Animation for Buttons */
  .loading button {
    position: relative;
    color: transparent;
  }

  .loading button::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* Smooth transitions for tab switching */
  #loginTab, #registerTab {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced form styling */
  .form-group input:focus + label,
  .form-group input:not(:placeholder-shown) + label {
    transform: translateY(-8px) scale(0.85);
    color: #475569;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .auth-container {
      flex-direction: column;
    }

    .auth-left-panel,
    .auth-right-panel {
      flex: none !important;
      width: 100% !important;
    }

    .auth-left-panel {
      min-height: 40vh;
    }

    .auth-right-panel {
      min-height: 60vh;
    }
  }

}