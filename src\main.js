import './style.css'
import { AuthForm } from './js/components/AuthForm.js'
import { ProfilePage } from './js/components/ProfilePage.js'
import authService from './js/services/authService.js'

// Initialize the application
class App {
  constructor() {
    this.currentPage = null;
    this.init();
  }

  init() {
    this.setupDOM();
    this.checkAuthState();
  }

  setupDOM() {
    document.querySelector('#app').innerHTML = `
      <div id="appContainer">
        <!-- Pages will be rendered here -->
      </div>
    `;
  }

  checkAuthState() {
    if (authService.isAuthenticated()) {
      this.showProfilePage();
    } else {
      this.showAuthPage();
    }
  }

  showAuthPage() {
    this.currentPage = 'auth';
    this.authForm = new AuthForm('appContainer', () => {
      this.showProfilePage();
    });
    this.authForm.checkAuthState();
  }

  showProfilePage() {
    this.currentPage = 'profile';
    this.profilePage = new ProfilePage('appContainer', () => {
      this.showAuthPage();
    });
  }
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new App();
});
